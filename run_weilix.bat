@echo off
echo ========================================
echo تشغيل خادم Weilix
echo ========================================

REM التحقق من وجود البيئة الافتراضية
if not exist "venv\Scripts\activate.bat" (
    echo خطأ: البيئة الافتراضية غير موجودة
    echo يرجى تشغيل setup_environment.bat أولاً
    pause
    exit /b 1
)

REM تفعيل البيئة الافتراضية
echo تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

REM الانتقال إلى مجلد services/core
cd services\core

REM التحقق من وجود الملفات المطلوبة
if not exist "main.py" (
    echo خطأ: ملف main.py غير موجود
    pause
    exit /b 1
)

echo ✓ بدء تشغيل الخادم على http://127.0.0.1:8080
echo ✓ اضغط Ctrl+C لإيقاف الخادم
echo ========================================

uvicorn main:app --host 0.0.0.0 --port 8080 --reload
@echo off
cd /d H:\weilix\weilix_mvp_v2\services\core
call .venv\Scripts\activate.bat
python -m uvicorn main:app --host 127.0.0.1 --port 8080 --reload
@echo off
cd /d H:\weilix\weilix_mvp_v2\services\core
python -m venv .venv
call .venv\Scripts\activate.bat
pip install -r requirements.txt
python -m uvicorn main:app --host 127.0.0.1 --port 8080 --reload
