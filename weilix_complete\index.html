<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Weilix MVP v2 - الصفحة الرئيسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .header h1 {
            font-size: 3em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        .interfaces {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .interface-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .interface-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        
        .interface-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .interface-card p {
            opacity: 0.9;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s;
            font-weight: bold;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .btn.primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
        }
        
        .btn.primary:hover {
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
        }
        
        .status {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .status.online {
            border-left: 4px solid #2ecc71;
        }
        
        .status.offline {
            border-left: 4px solid #e74c3c;
        }
        
        .features {
            margin-top: 40px;
            text-align: center;
        }
        
        .features h2 {
            margin-bottom: 20px;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Weilix MVP v2</h1>
            <p>نظام ذكي متكامل مع واجهات متعددة وإدارة شاملة</p>
        </div>
        
        <div class="interfaces">
            <div class="interface-card">
                <h3>💬 واجهة المستخدم</h3>
                <p>واجهة بسيطة وأنيقة للتفاعل مع Weilix. أرسل رسائلك واحصل على ردود ذكية فورية.</p>
                <a href="client.html" class="btn primary">فتح الواجهة</a>
            </div>
            
            <div class="interface-card">
                <h3>📊 لوحة التحكم</h3>
                <p>لوحة تحكم احترافية لإدارة الوكلاء والمهام ومراقبة النظام في الوقت الفعلي.</p>
                <a href="dashboard.html" class="btn">فتح لوحة التحكم</a>
            </div>
            
            <div class="interface-card">
                <h3>🔧 واجهة API</h3>
                <p>واجهة تفاعلية لاختبار جميع نقاط النهاية والتكامل مع النظام.</p>
                <a href="http://127.0.0.1:8080/docs" class="btn" target="_blank">فتح API Docs</a>
            </div>
            
            <div class="interface-card">
                <h3>🧪 اختبار بسيط</h3>
                <p>أداة اختبار سريعة للتحقق من عمل النظام وتشخيص المشاكل.</p>
                <a href="test_simple.html" class="btn">فتح الاختبار</a>
            </div>
        </div>
        
        <div id="status" class="status">
            <h3>🔍 فحص حالة النظام...</h3>
            <p>جاري التحقق من اتصال الخادم...</p>
        </div>
        
        <div class="features">
            <h2>✨ الميزات الرئيسية</h2>
            <div class="feature-list">
                <div class="feature">
                    <h4>🤖 ذكاء اصطناعي</h4>
                    <p>معالجة النصوص والردود الذكية</p>
                </div>
                <div class="feature">
                    <h4>👥 إدارة الوكلاء</h4>
                    <p>نظام ANAT/INANA المتقدم</p>
                </div>
                <div class="feature">
                    <h4>📋 إدارة المهام</h4>
                    <p>تنظيم وتتبع المهام بكفاءة</p>
                </div>
                <div class="feature">
                    <h4>🔒 الأمان</h4>
                    <p>نظام سياسات وتدقيق شامل</p>
                </div>
                <div class="feature">
                    <h4>📊 المراقبة</h4>
                    <p>مراقبة النظام في الوقت الفعلي</p>
                </div>
                <div class="feature">
                    <h4>🐳 Docker</h4>
                    <p>نشر سهل مع Docker Compose</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // فحص حالة الخادم
        async function checkServerStatus() {
            const statusEl = document.getElementById('status');
            
            try {
                const response = await fetch('http://127.0.0.1:8080/v1/health');
                const data = await response.json();
                
                if (response.ok) {
                    statusEl.className = 'status online';
                    statusEl.innerHTML = `
                        <h3>✅ النظام متصل ويعمل</h3>
                        <p>الإصدار: ${data.version} | الحالة: ${data.status}</p>
                        <p>يمكنك الآن استخدام جميع الواجهات</p>
                    `;
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                statusEl.className = 'status offline';
                statusEl.innerHTML = `
                    <h3>❌ الخادم غير متصل</h3>
                    <p>تأكد من تشغيل الخادم باستخدام: <code>run.bat</code></p>
                    <p>أو استخدم: <code>quick_start.bat</code> للإعداد والتشغيل التلقائي</p>
                `;
            }
        }
        
        // فحص الحالة عند تحميل الصفحة
        window.onload = checkServerStatus;
        
        // فحص دوري كل 30 ثانية
        setInterval(checkServerStatus, 30000);
    </script>
</body>
</html>
