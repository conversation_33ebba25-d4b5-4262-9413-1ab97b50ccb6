#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لتطبيق Weilix
"""

import requests
import json

# عنوان الخادم
BASE_URL = "http://127.0.0.1:8080"

def test_health():
    """اختبار صحة الخدمة"""
    print("🔍 اختبار صحة الخدمة...")
    try:
        response = requests.get(f"{BASE_URL}/v1/health")
        print(f"✅ الحالة: {response.status_code}")
        print(f"📄 الرد: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_infer(prompt):
    """اختبار الذكاء الاصطناعي"""
    print(f"🤖 اختبار الذكاء الاصطناعي مع: '{prompt}'")
    try:
        data = {"prompt": prompt}
        response = requests.post(
            f"{BASE_URL}/v1/infer",
            headers={"Content-Type": "application/json"},
            json=data
        )
        print(f"✅ الحالة: {response.status_code}")
        result = response.json()
        print(f"📄 الرد: {result.get('output', 'لا يوجد رد')}")
        return True
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_evolve():
    """اختبار جدولة التدريب"""
    print("🧠 اختبار جدولة التدريب...")
    try:
        response = requests.post(f"{BASE_URL}/v1/evolve")
        print(f"✅ الحالة: {response.status_code}")
        print(f"📄 الرد: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    print("=" * 50)
    print("🚀 اختبار تطبيق Weilix MVP v2")
    print("=" * 50)
    
    # اختبار صحة الخدمة
    if not test_health():
        print("❌ الخادم لا يعمل!")
        return
    
    print("\n" + "-" * 30)
    
    # اختبار الذكاء الاصطناعي
    test_infer("مرحبا يا ويليكس")
    
    print("\n" + "-" * 30)
    
    # اختبار رسالة أخرى
    test_infer("كيف حالك؟")
    
    print("\n" + "-" * 30)
    
    # اختبار التدريب
    test_evolve()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار!")
    print("🌐 يمكنك الآن استخدام:")
    print("   - واجهة الويب: client.html")
    print("   - واجهة API: http://127.0.0.1:8080/docs")
    print("=" * 50)

if __name__ == "__main__":
    main()
