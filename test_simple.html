<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>اختبار بسيط لويليكس</title>
</head>
<body>
    <h1>اختبار بسيط</h1>
    <button onclick="testAPI()">اختبار API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'جاري الاختبار...';
            
            try {
                // اختبار 1: فحص الصحة
                console.log('اختبار فحص الصحة...');
                const healthRes = await fetch('http://127.0.0.1:8080/v1/health');
                const healthData = await healthRes.json();
                console.log('نتيجة فحص الصحة:', healthData);
                
                // اختبار 2: إرسال رسالة
                console.log('اختبار إرسال رسالة...');
                const inferRes = await fetch('http://127.0.0.1:8080/v1/infer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: 'مرحبا',
                        context: []
                    })
                });
                
                const inferData = await inferRes.json();
                console.log('نتيجة الرسالة:', inferData);
                
                resultDiv.innerHTML = `
                    <h3>نتائج الاختبار:</h3>
                    <p><strong>فحص الصحة:</strong> ${JSON.stringify(healthData)}</p>
                    <p><strong>الرسالة:</strong> ${JSON.stringify(inferData)}</p>
                    <p><strong>الرد:</strong> ${inferData.output || 'لا يوجد رد'}</p>
                `;
                
            } catch (error) {
                console.error('خطأ:', error);
                resultDiv.innerHTML = `<p style="color: red;">خطأ: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>

<!doctype html><meta charset="utf-8">
<title>Weilix Quick Test</title>
<style>body{font-family:system-ui;background:#111;color:#eee;padding:20px} code{background:#222;padding:2px 6px;border-radius:6px}</style>
<h2>اختبار سريع</h2>
<p>قاعدة API: <code id="b">http://127.0.0.1:8080</code></p>
<button onclick="go('/v1/health')">/v1/health</button>
<button onclick="go('/v1/db-ping')">/v1/db-ping</button>
<button onclick="infer()">/v1/infer</button>
<pre id="out">جاهز…</pre>
<script>
  const base=document.getElementById('b').textContent;
  async function go(p){ const out=document.getElementById('out'); out.textContent='جارٍ…'; const r=await fetch(base+p); out.textContent=await r.text(); }
  async function infer(){ const out=document.getElementById('out'); out.textContent='جارٍ…'; const r=await fetch(base+'/v1/infer',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({prompt:'مرحبا يا ويليكس',context:[]})}); out.textContent=await r.text(); }
</script>

