#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم اختبار لخوارزمية تقدير المخاطرة
"""

import json
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# ===== Risk Scoring (Tasks) =====
HIGH_KEYWORDS = {"اختراق", "تخطي", "تعطيل", "تجسس", "root", "priv-esc", "privilege", "phishing", "ddos", "هجوم", "malware"}
MED_KEYWORDS  = {"تحذير", "ثغرة", "leak", "leaks", "bruteforce", "anomaly", "مشبوه", "suspicious"}

def score_task_risk(title: str, details: dict | None) -> tuple[int, int]:
    """
    يعيد (risk_score, suggested_priority)
    risk_score: 0..100
    suggested_priority: 1 (عالي) / 2 (عادي) / 3 (منخفض)
    """
    text = (title or "")
    if details:
        try:
            text += " " + json.dumps(details, ensure_ascii=False)
        except Exception:
            pass
    text_norm = text.lower()

    score = 0
    for kw in HIGH_KEYWORDS:
        if kw in text_norm:
            score += 25
    for kw in MED_KEYWORDS:
        if kw in text_norm:
            score += 10

    score = max(0, min(100, score))
    if score >= 40:
        pr = 1
    elif score >= 15:
        pr = 2
    else:
        pr = 3
    return score, pr

# ===== نماذج API =====
class RiskAssessmentRequest(BaseModel):
    title: str
    details: Optional[dict] = None

class RiskAssessmentResponse(BaseModel):
    risk_score: int  # 0-100
    suggested_priority: int  # 1, 2, 3
    risk_level: str  # "عالي", "متوسط", "منخفض"
    detected_keywords: List[str]
    analysis: Dict[str, Any]

# ===== تطبيق FastAPI =====
app = FastAPI(title="Risk Assessment API", version="test-v1")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"message": "خادم اختبار خوارزمية تقدير المخاطرة", "version": "test-v1"}

@app.get("/health")
def health():
    return {"status": "ok", "version": "test-v1", "service": "risk-assessment"}

@app.post("/assess-risk", response_model=RiskAssessmentResponse)
def assess_task_risk(req: RiskAssessmentRequest):
    """تقدير مخاطرة المهمة"""
    risk_score, suggested_priority = score_task_risk(req.title, req.details)
    
    # تحديد مستوى المخاطرة
    if risk_score >= 40:
        risk_level = "عالي"
    elif risk_score >= 15:
        risk_level = "متوسط"
    else:
        risk_level = "منخفض"
    
    # العثور على الكلمات المكتشفة
    text = (req.title or "")
    if req.details:
        try:
            text += " " + json.dumps(req.details, ensure_ascii=False)
        except Exception:
            pass
    text_norm = text.lower()
    
    detected_keywords = []
    high_keywords_found = []
    med_keywords_found = []
    
    for kw in HIGH_KEYWORDS:
        if kw in text_norm:
            detected_keywords.append(kw)
            high_keywords_found.append(kw)
    
    for kw in MED_KEYWORDS:
        if kw in text_norm:
            detected_keywords.append(kw)
            med_keywords_found.append(kw)
    
    # تحليل مفصل
    analysis = {
        "text_analyzed": text,
        "text_length": len(text),
        "high_risk_keywords": high_keywords_found,
        "medium_risk_keywords": med_keywords_found,
        "total_keywords_found": len(detected_keywords),
        "score_breakdown": {
            "high_keywords_score": len(high_keywords_found) * 25,
            "medium_keywords_score": len(med_keywords_found) * 10,
            "total_score": risk_score
        },
        "priority_mapping": {
            1: "عالي (40+ نقطة)",
            2: "عادي (15-39 نقطة)", 
            3: "منخفض (0-14 نقطة)"
        },
        "timestamp": datetime.now().isoformat()
    }
    
    return RiskAssessmentResponse(
        risk_score=risk_score,
        suggested_priority=suggested_priority,
        risk_level=risk_level,
        detected_keywords=detected_keywords,
        analysis=analysis
    )

# اختبارات مدمجة
@app.get("/test-cases")
def get_test_cases():
    """إرجاع حالات اختبار جاهزة"""
    return {
        "test_cases": [
            {
                "title": "فحص أمني عادي",
                "details": {"type": "security_scan"},
                "expected_level": "منخفض"
            },
            {
                "title": "تحذير من ثغرة أمنية",
                "details": {"severity": "medium"},
                "expected_level": "متوسط"
            },
            {
                "title": "محاولة اختراق النظام",
                "details": {"method": "privilege escalation"},
                "expected_level": "عالي"
            },
            {
                "title": "هجوم DDoS مكتشف",
                "details": {"attack_type": "ddos", "source": "unknown"},
                "expected_level": "عالي"
            },
            {
                "title": "تقرير يومي",
                "details": {"type": "daily_report"},
                "expected_level": "منخفض"
            },
            {
                "title": "اكتشاف malware في النظام",
                "details": {"file": "suspicious.exe", "action": "quarantine"},
                "expected_level": "عالي"
            }
        ]
    }

@app.post("/run-tests")
def run_automated_tests():
    """تشغيل اختبارات تلقائية"""
    test_cases = get_test_cases()["test_cases"]
    results = []
    
    for test_case in test_cases:
        req = RiskAssessmentRequest(
            title=test_case["title"],
            details=test_case["details"]
        )
        
        result = assess_task_risk(req)
        
        test_result = {
            "input": test_case,
            "output": result.dict(),
            "passed": result.risk_level == test_case["expected_level"]
        }
        
        results.append(test_result)
    
    passed_count = sum(1 for r in results if r["passed"])
    total_count = len(results)
    
    return {
        "summary": {
            "total_tests": total_count,
            "passed": passed_count,
            "failed": total_count - passed_count,
            "success_rate": f"{(passed_count/total_count)*100:.1f}%"
        },
        "results": results
    }

if __name__ == "__main__":
    print("🚀 بدء تشغيل خادم اختبار خوارزمية تقدير المخاطرة")
    print("📍 الخادم سيعمل على: http://127.0.0.1:8081")
    print("📖 التوثيق متاح على: http://127.0.0.1:8081/docs")
    uvicorn.run("test_risk_server:app", host="0.0.0.0", port=8081, reload=True)
