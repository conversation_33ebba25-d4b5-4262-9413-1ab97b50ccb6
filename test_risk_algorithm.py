#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار خوارزمية تقدير المخاطرة بدون خادم
"""

import json

# ===== Risk Scoring (Tasks) =====
HIGH_KEYWORDS = {"اختراق", "تخطي", "تعطيل", "تجسس", "root", "priv-esc", "privilege", "phishing", "ddos", "هجوم", "malware"}
MED_KEYWORDS  = {"تحذير", "ثغرة", "leak", "leaks", "bruteforce", "anomaly", "مشبوه", "suspicious"}

def score_task_risk(title: str, details: dict | None) -> tuple[int, int]:
    """
    يعيد (risk_score, suggested_priority)
    risk_score: 0..100
    suggested_priority: 1 (عالي) / 2 (عادي) / 3 (منخفض)
    """
    text = (title or "")
    if details:
        try:
            text += " " + json.dumps(details, ensure_ascii=False)
        except Exception:
            pass
    text_norm = text.lower()

    score = 0
    detected_high = []
    detected_med = []
    
    for kw in HIGH_KEYWORDS:
        if kw in text_norm:
            score += 25
            detected_high.append(kw)
    for kw in MED_KEYWORDS:
        if kw in text_norm:
            score += 10
            detected_med.append(kw)

    score = max(0, min(100, score))
    if score >= 40:
        pr = 1
    elif score >= 15:
        pr = 2
    else:
        pr = 3
    
    return score, pr, detected_high, detected_med

def test_risk_algorithm():
    """اختبار شامل لخوارزمية تقدير المخاطرة"""
    
    print("🔍 اختبار خوارزمية تقدير المخاطرة")
    print("=" * 60)
    
    test_cases = [
        {
            "title": "فحص أمني عادي",
            "details": {"type": "security_scan"},
            "expected_level": "منخفض"
        },
        {
            "title": "تحذير من ثغرة أمنية",
            "details": {"severity": "medium"},
            "expected_level": "متوسط"
        },
        {
            "title": "محاولة اختراق النظام",
            "details": {"method": "privilege escalation"},
            "expected_level": "عالي"
        },
        {
            "title": "هجوم DDoS مكتشف",
            "details": {"attack_type": "ddos", "source": "unknown"},
            "expected_level": "عالي"
        },
        {
            "title": "تقرير يومي",
            "details": {"type": "daily_report"},
            "expected_level": "منخفض"
        },
        {
            "title": "اكتشاف malware في النظام",
            "details": {"file": "suspicious.exe", "action": "quarantine"},
            "expected_level": "عالي"
        },
        {
            "title": "محاولة تجسس على البيانات",
            "details": {"target": "database", "method": "sql_injection"},
            "expected_level": "عالي"
        },
        {
            "title": "تسريب leak في قاعدة البيانات",
            "details": {"affected_records": 1000},
            "expected_level": "متوسط"
        },
        {
            "title": "نشاط مشبوه في الشبكة",
            "details": {"anomaly_score": 0.8},
            "expected_level": "متوسط"
        },
        {
            "title": "صيانة دورية للخادم",
            "details": {"maintenance_type": "routine"},
            "expected_level": "منخفض"
        }
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 اختبار {i}: {test_case['title']}")
        print(f"   📝 التفاصيل: {test_case['details']}")
        
        # تشغيل الخوارزمية
        risk_score, suggested_priority, detected_high, detected_med = score_task_risk(
            test_case["title"], 
            test_case["details"]
        )
        
        # تحديد مستوى المخاطرة
        if risk_score >= 40:
            risk_level = "عالي"
        elif risk_score >= 15:
            risk_level = "متوسط"
        else:
            risk_level = "منخفض"
        
        # عرض النتائج
        print(f"   📊 درجة المخاطرة: {risk_score}/100")
        print(f"   🎯 مستوى المخاطرة: {risk_level}")
        print(f"   ⚡ الأولوية المقترحة: {suggested_priority}")
        
        if detected_high:
            print(f"   🚨 كلمات عالية المخاطرة: {detected_high}")
        if detected_med:
            print(f"   ⚠️ كلمات متوسطة المخاطرة: {detected_med}")
        
        # التحقق من النتيجة المتوقعة
        if risk_level == test_case['expected_level']:
            print(f"   ✅ النتيجة صحيحة!")
            passed_tests += 1
        else:
            print(f"   ❌ النتيجة غير متوقعة (متوقع: {test_case['expected_level']})")
    
    # ملخص النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    print(f"   ✅ اختبارات ناجحة: {passed_tests}/{total_tests}")
    print(f"   ❌ اختبارات فاشلة: {total_tests - passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! الخوارزمية تعمل بشكل مثالي.")
    else:
        print("⚠️ بعض الاختبارات فشلت. قد تحتاج الخوارزمية لتحسين.")
    
    print("=" * 60)

def test_edge_cases():
    """اختبار حالات خاصة"""
    
    print("\n🔬 اختبار الحالات الخاصة")
    print("-" * 40)
    
    edge_cases = [
        ("", None),  # نص فارغ
        ("نص عادي بدون كلمات مفتاحية", {}),  # لا توجد كلمات مخاطرة
        ("اختراق هجوم malware ddos", {"severity": "critical"}),  # كلمات متعددة عالية المخاطرة
        ("تحذير ثغرة leak anomaly", None),  # كلمات متعددة متوسطة المخاطرة
        ("PHISHING ATTACK ROOT PRIVILEGE", {"type": "security_incident"}),  # أحرف كبيرة
    ]
    
    for i, (title, details) in enumerate(edge_cases, 1):
        print(f"\n🧪 حالة خاصة {i}: '{title}'")
        risk_score, suggested_priority, detected_high, detected_med = score_task_risk(title, details)
        
        print(f"   📊 درجة المخاطرة: {risk_score}")
        print(f"   ⚡ الأولوية: {suggested_priority}")
        print(f"   🚨 كلمات عالية: {detected_high}")
        print(f"   ⚠️ كلمات متوسطة: {detected_med}")

def demonstrate_algorithm():
    """عرض توضيحي للخوارزمية"""
    
    print("\n🎯 عرض توضيحي لخوارزمية تقدير المخاطرة")
    print("=" * 60)
    
    print("📋 قواعد التقييم:")
    print("   🚨 كلمات عالية المخاطرة (+25 نقطة لكل كلمة):")
    print(f"      {', '.join(list(HIGH_KEYWORDS)[:10])}...")
    print("   ⚠️ كلمات متوسطة المخاطرة (+10 نقاط لكل كلمة):")
    print(f"      {', '.join(list(MED_KEYWORDS)[:10])}...")
    
    print("\n🎯 مستويات الأولوية:")
    print("   🔴 عالي (1): 40+ نقطة")
    print("   🟡 عادي (2): 15-39 نقطة")
    print("   🟢 منخفض (3): 0-14 نقطة")
    
    print("\n💡 أمثلة تفاعلية:")
    
    examples = [
        "محاولة اختراق الخادم",
        "تحذير من ثغرة أمنية",
        "تقرير صيانة دورية"
    ]
    
    for example in examples:
        risk_score, priority, high_kw, med_kw = score_task_risk(example, None)
        level = "عالي" if risk_score >= 40 else "متوسط" if risk_score >= 15 else "منخفض"
        print(f"   📝 '{example}' → {risk_score} نقطة → مستوى {level}")

if __name__ == "__main__":
    print("🚀 اختبار شامل لخوارزمية تقدير المخاطرة")
    print("تم تطوير هذه الخوارزمية لتقييم مستوى المخاطرة في المهام تلقائياً")
    print()
    
    # عرض توضيحي
    demonstrate_algorithm()
    
    # اختبار الخوارزمية
    test_risk_algorithm()
    
    # اختبار الحالات الخاصة
    test_edge_cases()
    
    print("\n🎉 انتهى الاختبار! الخوارزمية جاهزة للاستخدام في main.py")
