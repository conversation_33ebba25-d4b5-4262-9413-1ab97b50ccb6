@echo off
echo ========================================
echo Starting Weilix Server
echo ========================================

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found
    echo Please run setup.bat first
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Navigate to services/core
cd services\core

REM Check if main.py exists
if not exist "main.py" (
    echo ERROR: main.py not found
    pause
    exit /b 1
)

echo Starting server on http://127.0.0.1:8080
echo Press Ctrl+C to stop the server
echo ========================================

uvicorn main:app --host 0.0.0.0 --port 8080 --reload
