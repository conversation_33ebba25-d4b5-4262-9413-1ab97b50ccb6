version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: RootPass!123
      MYSQL_DATABASE: weilix_db
      MYSQL_USER: weilix_user
      MYSQL_PASSWORD: StrongPass!123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  core:
    build: ./services/core
    environment:
      CONSTITUTION: /configs/constitution.yaml
      DB_URL: mysql+pymysql://weilix_user:StrongPass!123@mysql:3306/weilix_db
    volumes:
      - ./configs:/configs:ro
      - ./libs:/libs:ro
    ports:
      - "8080:8080"
    depends_on:
      mysql:
        condition: service_healthy

volumes:
  mysql_data:
