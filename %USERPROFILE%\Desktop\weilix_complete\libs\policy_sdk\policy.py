import hashlib, json
from typing import Dict, Any, Tuple

class PolicyRouter:
    def __init__(self, policy: Dict[str, Any]):
        self.policy = policy or {}

    def precheck(self, prompt: str) -> Tuple[bool, str]:
        lower = prompt.lower()
        blocked = ["make a bomb", "ransomware", "malware"]
        if any(b in lower for b in blocked):
            return False, "طلب مرفوض: محتوى أمني مرتفع."
        return True, ""

    def postcheck(self, output: str) -> str:
        if any(k in output.lower() for k in ["diagnosis", "investment", "legal", "medical"]):
            output += "\n\n— تنبيه: هذه معلومات عامة وليست نصيحة مهنية."
        return output

    def hash_chain(self, last_hash: str, record: Dict[str, Any]) -> str:
        payload = (last_hash or "").encode() + json.dumps(record, ensure_ascii=False, sort_keys=True).encode()
        return hashlib.sha256(payload).hexdigest()
