import os
import sys
from pathlib import Path
from typing import List, Optional
from datetime import datetime

import uvicorn
import yaml
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# ===== ربط Policy SDK =====
sys.path.append(str((Path(__file__).resolve().parents[2] / "libs")))
from policy_sdk.policy import PolicyRouter  # type: ignore

# ===== قاعدة البيانات (SQLAlchemy) =====
from sqlalchemy import (
    create_engine, text, String, Enum, Text, JSON,
    TIMESTAMP, ForeignKey, Integer, BigInteger
)
from sqlalchemy.orm import (
    sessionmaker, DeclarativeBase, Mapped, mapped_column, Session
)

# بيانات الاتصال (عدّل كلمة السر إذا غيّرتها)
DB_URL = os.environ.get(
    "DB_URL",
    "mysql+pymysql://weilix_user:StrongPass!123@127.0.0.1:3306/weilix_db"
)
engine = create_engine(DB_URL, pool_pre_ping=True, future=True)
SessionLocal = sessionmaker(bind=engine, autoflush=False, autocommit=False, future=True)

class Base(DeclarativeBase):
    pass

# ========== جداول ORM ==========
class Agent(Base):
    __tablename__ = "agents"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    code: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)  # anat-1 / inana-3
    kind: Mapped[str] = mapped_column(Enum("ANAT", "INANA", name="agent_kind"), nullable=False)
    status: Mapped[str] = mapped_column(Enum("ACTIVE", "PAUSED", "RETIRED", name="agent_status"), default="ACTIVE")
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class Task(Base):
    __tablename__ = "tasks"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    agent_id: Mapped[int] = mapped_column(BigInteger, ForeignKey("agents.id"), nullable=False)
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    details: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    priority: Mapped[int] = mapped_column(Integer, default=2)  # 1 عالي / 2 عادي / 3 منخفض
    state: Mapped[str] = mapped_column(Enum("QUEUED", "RUNNING", "DONE", "FAILED", name="task_state"), default="QUEUED")
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class Message(Base):
    __tablename__ = "messages"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    agent_id: Mapped[Optional[int]] = mapped_column(BigInteger, ForeignKey("agents.id"), nullable=True)
    role: Mapped[str] = mapped_column(Enum("USER", "WEILIX", "SYSTEM", name="msg_role"), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    meta: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class PolicyDoc(Base):
    __tablename__ = "policies"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    policy_id: Mapped[str] = mapped_column(String(128), nullable=False)
    version: Mapped[str] = mapped_column(String(32), nullable=False)
    body: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

class AuditLog(Base):
    __tablename__ = "audit_logs"
    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    event: Mapped[str] = mapped_column(String(64), nullable=False)
    payload: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    prev_hash: Mapped[Optional[str]] = mapped_column(String(64), nullable=True)
    hash: Mapped[str] = mapped_column(String(64), nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.utcnow)

# إنشاء الجداول إن لم تكن موجودة
Base.metadata.create_all(bind=engine)

# تبعية جلسة DB
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# ===== إعداد التطبيق/السياسات =====
APP_VERSION = "weilix-core-v2"
CONSTITUTION_PATH = os.environ.get(
    "CONSTITUTION",
    str(Path(__file__).resolve().parents[2] / "configs" / "constitution.yaml")
)
with open(CONSTITUTION_PATH, "r", encoding="utf-8") as f:
    POLICY_DOC = yaml.safe_load(f)
policy_router = PolicyRouter(POLICY_DOC)

LAST_HASH = ""

# ===== نماذج API =====
class InferReq(BaseModel):
    prompt: str
    context: List[str] = Field(default_factory=list)  # بدلاً من [] لتجنب mutable default

class InferRes(BaseModel):
    output: str
    model_version: str
    audit_hash: str
    # إخفاء تحذير Pydantic بشأن model_
    model_config = {"protected_namespaces": ()}

class AgentCreate(BaseModel):
    code: str = Field(..., examples=["anat-1"])
    kind: str = Field(..., pattern="^(ANAT|INANA)$")

class AgentOut(BaseModel):
    id: int
    code: str
    kind: str
    status: str
    created_at: datetime

class TaskCreate(BaseModel):
    agent_id: int
    title: str
    details: Optional[dict] = None
    priority: int = 2

class TaskOut(BaseModel):
    id: int
    agent_id: int
    title: str
    details: Optional[dict]
    priority: int
    state: str
    created_at: datetime

# ===== تطبيق FastAPI =====
app = FastAPI(title="Weilix API", version=APP_VERSION)

# ملاحظة: لا يجوز set allow_credentials=True مع "*" لذا جعلناها False
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://127.0.0.1:8080",
        "http://localhost:8080",
        "https://127.0.0.1:8443",
        "https://localhost:8443",
        "*",
    ],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ===== مسارات النظام =====
@app.get("/v1/health")
def health():
    return {"status": "ok", "version": APP_VERSION}

@app.get("/v1/db-ping")
def db_ping():
    try:
        with engine.connect() as conn:
            r = conn.execute(text("SELECT 1")).scalar()
            return {"db": "ok" if r == 1 else "fail"}
    except Exception as e:
        return {"db": "error", "detail": str(e)}

# ===== مسارات الذكاء =====
@app.post("/v1/infer", response_model=InferRes)
def infer(req: InferReq):
    global LAST_HASH
    ok, msg = policy_router.precheck(req.prompt)
    if not ok:
        raise HTTPException(status_code=400, detail=msg)

    out = f"【Weilix】 استلمت: {req.prompt}" if req.prompt.strip() else "قل لي ماذا تريد أن أفعل."
    out = policy_router.postcheck(out)

    record = {
        "ts": datetime.utcnow().isoformat() + "Z",
        "endpoint": "/v1/infer",
        "input_len": len(req.prompt),
        "output_len": len(out),
        "version": APP_VERSION,
    }
    LAST_HASH = policy_router.hash_chain(LAST_HASH, record)

    return InferRes(output=out, model_version=APP_VERSION, audit_hash=LAST_HASH)

@app.post("/v1/evolve")
def evolve():
    global LAST_HASH
    task = {
        "ts": datetime.utcnow().isoformat() + "Z",
        "endpoint": "/v1/evolve",
        "action": "schedule_training",
    }
    LAST_HASH = policy_router.hash_chain(LAST_HASH, task)
    return {"status": "scheduled", "audit_hash": LAST_HASH, "version": APP_VERSION}

# ===== مسارات قاعدة البيانات =====
@app.post("/v1/agents", response_model=AgentOut)
def create_agent(body: AgentCreate, db: Session = Depends(get_db)):
    exists = db.query(Agent).filter(Agent.code == body.code).first()
    if exists:
        raise HTTPException(status_code=409, detail="Agent code already exists")
    agent = Agent(code=body.code, kind=body.kind)
    db.add(agent)
    db.commit()
    db.refresh(agent)
    return AgentOut(
        id=agent.id, code=agent.code, kind=agent.kind,
        status=agent.status, created_at=agent.created_at
    )

@app.get("/v1/agents", response_model=List[AgentOut])
def list_agents(db: Session = Depends(get_db)):
    rows = db.query(Agent).order_by(Agent.id.desc()).all()
    return [
        AgentOut(
            id=a.id, code=a.code, kind=a.kind,
            status=a.status, created_at=a.created_at
        )
        for a in rows
    ]

@app.post("/v1/tasks", response_model=TaskOut)
def create_task(body: TaskCreate, db: Session = Depends(get_db)):
    agent = db.query(Agent).filter(Agent.id == body.agent_id).first()
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    task = Task(
        agent_id=body.agent_id, title=body.title,
        details=body.details, priority=body.priority
    )
    db.add(task)
    db.commit()
    db.refresh(task)
    return TaskOut(
        id=task.id, agent_id=task.agent_id, title=task.title,
        details=task.details, priority=task.priority,
        state=task.state, created_at=task.created_at
    )

@app.get("/v1/tasks", response_model=List[TaskOut])
def list_tasks(agent_id: Optional[int] = None, db: Session = Depends(get_db)):
    q = db.query(Task)
    if agent_id is not None:
        q = q.filter(Task.agent_id == agent_id)
    rows = q.order_by(Task.id.desc()).all()
    return [
        TaskOut(
            id=t.id, agent_id=t.agent_id, title=t.title,
            details=t.details, priority=t.priority,
            state=t.state, created_at=t.created_at
        )
        for t in rows
    ]

if __name__ == "__main__":
    # شغّل محليًا على 8080 (HTTP). لو ترغب SSL استخدم أوامر uvicorn من الطرفية
    uvicorn.run("main:app", host="0.0.0.0", port=8080, reload=True)
