#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار خوارزمية تقدير المخاطرة
"""

import requests
import json

# عنوان الخادم
BASE_URL = "http://127.0.0.1:8080"

def test_risk_assessment():
    """اختبار نقطة نهاية تقدير المخاطرة"""
    
    test_cases = [
        {
            "title": "فحص أمني عادي",
            "details": {"type": "security_scan"},
            "expected_level": "منخفض"
        },
        {
            "title": "تحذير من ثغرة أمنية",
            "details": {"severity": "medium"},
            "expected_level": "متوسط"
        },
        {
            "title": "محاولة اختراق النظام",
            "details": {"method": "privilege escalation"},
            "expected_level": "عالي"
        },
        {
            "title": "هجوم DDoS مكتشف",
            "details": {"attack_type": "ddos", "source": "unknown"},
            "expected_level": "عالي"
        },
        {
            "title": "تقرير يومي",
            "details": {"type": "daily_report"},
            "expected_level": "منخفض"
        }
    ]
    
    print("🔍 اختبار خوارزمية تقدير المخاطرة")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 اختبار {i}: {test_case['title']}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/v1/assess-risk",
                headers={"Content-Type": "application/json"},
                json={
                    "title": test_case["title"],
                    "details": test_case["details"]
                }
            )
            
            if response.ok:
                result = response.json()
                print(f"   📊 درجة المخاطرة: {result['risk_score']}/100")
                print(f"   🎯 مستوى المخاطرة: {result['risk_level']}")
                print(f"   ⚡ الأولوية المقترحة: {result['suggested_priority']}")
                print(f"   🔍 كلمات مكتشفة: {result['detected_keywords']}")
                
                # التحقق من النتيجة المتوقعة
                if result['risk_level'] == test_case['expected_level']:
                    print(f"   ✅ النتيجة صحيحة!")
                else:
                    print(f"   ❌ النتيجة غير متوقعة (متوقع: {test_case['expected_level']})")
            else:
                print(f"   ❌ خطأ HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {e}")

def test_task_creation_with_risk():
    """اختبار إنشاء مهمة مع تقدير المخاطرة التلقائي"""
    
    print("\n" + "=" * 50)
    print("🔧 اختبار إنشاء مهمة مع تقدير المخاطرة")
    print("=" * 50)
    
    # أولاً، إنشاء وكيل للاختبار
    try:
        agent_response = requests.post(
            f"{BASE_URL}/v1/agents",
            headers={"Content-Type": "application/json"},
            json={
                "code": "test-agent-risk",
                "kind": "ANAT"
            }
        )
        
        if agent_response.ok:
            agent = agent_response.json()
            agent_id = agent["id"]
            print(f"✅ تم إنشاء وكيل اختبار: {agent_id}")
        else:
            print("❌ فشل في إنشاء وكيل الاختبار")
            return
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الوكيل: {e}")
        return
    
    # اختبار إنشاء مهام مختلفة
    test_tasks = [
        {
            "title": "مراجعة أمنية روتينية",
            "details": {"type": "routine_check"}
        },
        {
            "title": "التحقق من محاولة اختراق",
            "details": {"alert_type": "intrusion_attempt", "severity": "high"}
        }
    ]
    
    for task_data in test_tasks:
        try:
            task_response = requests.post(
                f"{BASE_URL}/v1/tasks",
                headers={"Content-Type": "application/json"},
                json={
                    "agent_id": agent_id,
                    "title": task_data["title"],
                    "details": task_data["details"],
                    "priority": 2  # أولوية افتراضية، ستتغير حسب تقدير المخاطرة
                }
            )
            
            if task_response.ok:
                task = task_response.json()
                print(f"\n📋 مهمة: {task['title']}")
                print(f"   📊 درجة المخاطرة: {task['risk_score']}/100")
                print(f"   ⚡ الأولوية النهائية: {task['priority']}")
                print(f"   🆔 معرف المهمة: {task['id']}")
            else:
                print(f"❌ فشل في إنشاء المهمة: {task_response.status_code}")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء المهمة: {e}")

def main():
    print("🚀 اختبار شامل لخوارزمية تقدير المخاطرة")
    print("=" * 60)
    
    # فحص الاتصال أولاً
    try:
        health_response = requests.get(f"{BASE_URL}/v1/health")
        if health_response.ok:
            print("✅ الخادم متصل ويعمل")
        else:
            print("❌ الخادم لا يستجيب")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return
    
    # تشغيل الاختبارات
    test_risk_assessment()
    test_task_creation_with_risk()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار!")

if __name__ == "__main__":
    main()
