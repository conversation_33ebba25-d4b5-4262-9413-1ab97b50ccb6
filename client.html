<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>Weilix Client</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            padding: 20px;
            background-color: #f5f5f5;
            max-width: 800px;
            margin: 0 auto;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            resize: vertical;
            font-family: inherit;
        }
        textarea:focus {
            border-color: #3498db;
            outline: none;
        }
        .button-container {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 12px 30px;
            font-size: 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .response-section {
            margin-top: 30px;
        }
        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        h3 {
            color: #2c3e50;
            margin: 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.loading { background-color: #fff3cd; color: #856404; }
        pre {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            white-space: pre-wrap;
            word-wrap: break-word;
            min-height: 60px;
            font-family: 'Courier New', monospace;
        }
        .weilix-response {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            color: #2d5a2d;
            font-family: inherit;
            font-size: 16px;
            line-height: 1.5;
        }
        .loading {
            display: none;
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .test-buttons {
            margin-top: 15px;
            text-align: center;
        }
        .test-btn {
            background-color: #95a5a6;
            padding: 8px 15px;
            margin: 5px;
            font-size: 14px;
        }
        .test-btn:hover {
            background-color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🤖 واجهة ويليكس</h2>

        <textarea id="prompt" placeholder="اكتب رسالتك هنا... مثل: مرحبا يا ويليكس"></textarea>

        <div class="button-container">
            <button id="sendBtn" onclick="send()">إرسال</button>
        </div>

        <div class="test-buttons">
            <button class="test-btn" onclick="testMessage('مرحبا يا ويليكس')">اختبار: مرحبا</button>
            <button class="test-btn" onclick="testMessage('كيف حالك؟')">اختبار: كيف حالك</button>
            <button class="test-btn" onclick="testMessage('ما هو اسمك؟')">اختبار: ما اسمك</button>
        </div>

        <div class="response-section">
            <div class="response-header">
                <h3>الرد:</h3>
                <span id="status" class="status"></span>
            </div>
            <div class="loading" id="loading">⏳ جاري المعالجة...</div>
            <pre id="response">لم يتم إرسال أي رسالة بعد...</pre>
        </div>
    </div>

    <script>
        function setStatus(type, message) {
            const statusEl = document.getElementById("status");
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        function setLoading(isLoading) {
            const loadingEl = document.getElementById("loading");
            const sendBtn = document.getElementById("sendBtn");
            const responseEl = document.getElementById("response");

            if (isLoading) {
                loadingEl.style.display = "block";
                sendBtn.disabled = true;
                sendBtn.textContent = "جاري الإرسال...";
                setStatus("loading", "معالجة");
            } else {
                loadingEl.style.display = "none";
                sendBtn.disabled = false;
                sendBtn.textContent = "إرسال";
            }
        }

        function testMessage(message) {
            document.getElementById("prompt").value = message;
            send();
        }

        async function send() {
            try {
                const prompt = document.getElementById("prompt").value.trim();
                const responseEl = document.getElementById("response");

                if (!prompt) {
                    responseEl.textContent = "يرجى كتابة رسالة أولاً";
                    setStatus("error", "خطأ");
                    return;
                }

                setLoading(true);
                responseEl.textContent = "جاري إرسال الطلب...";

                console.log("إرسال الطلب:", prompt);

                const res = await fetch("http://127.0.0.1:8080/v1/infer", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        context: []
                    })
                });

                console.log("حالة الاستجابة:", res.status);

                if (!res.ok) {
                    const errorText = await res.text();
                    console.error("خطأ في الاستجابة:", errorText);
                    throw new Error(`خطأ HTTP ${res.status}: ${errorText}`);
                }

                const data = await res.json();
                console.log("البيانات المستلمة:", data);

                // عرض الرد بشكل جميل
                if (data.output) {
                    responseEl.textContent = data.output;
                    responseEl.className = "weilix-response";
                    setStatus("success", "تم بنجاح");
                } else {
                    responseEl.textContent = JSON.stringify(data, null, 2);
                    responseEl.className = "";
                    setStatus("success", "تم");
                }

            } catch (error) {
                console.error('خطأ:', error);
                const responseEl = document.getElementById("response");
                responseEl.textContent = `خطأ: ${error.message}\n\nتأكد من أن الخادم يعمل على http://127.0.0.1:8080`;
                responseEl.className = "";
                setStatus("error", "فشل");
            } finally {
                setLoading(false);
            }
        }

        // اختبار الاتصال عند تحميل الصفحة
        window.onload = async function() {
            try {
                const res = await fetch("http://127.0.0.1:8080/v1/health");
                if (res.ok) {
                    setStatus("success", "متصل");
                    document.getElementById("response").textContent = "✅ الخادم متصل ويعمل بشكل طبيعي\nيمكنك الآن إرسال رسائلك";
                } else {
                    throw new Error("الخادم لا يستجيب");
                }
            } catch (error) {
                setStatus("error", "غير متصل");
                document.getElementById("response").textContent = "❌ لا يمكن الاتصال بالخادم\nتأكد من تشغيل الخادم على http://127.0.0.1:8080";
            }
        };

        // إرسال عند الضغط على Enter
        document.getElementById("prompt").addEventListener("keypress", function(event) {
            if (event.key === "Enter" && !event.shiftKey) {
                event.preventDefault();
                send();
            }
        });
    </script>
</body>
</html>
<!DOCTYPE html><html lang="ar" dir="rtl"><head>
<meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Weilix Chat</title>
<style>
  body{font-family:system-ui;margin:0;background:#0b1020;color:#eaf0ff}
  .wrap{max-width:900px;margin:30px auto;padding:16px}
  .row{display:flex;gap:8px}
  input,button,textarea{background:#101836;border:1px solid #243055;border-radius:10px;color:#eaf0ff}
  textarea{flex:1;min-height:80px;padding:10px}
  input{padding:10px;width:280px}
  button{padding:10px 16px;cursor:pointer}
  .log{white-space:pre-wrap;background:#0d1530;border:1px dashed #2b375f;border-radius:10px;padding:10px;margin-top:12px;min-height:100px}
</style>
</head><body><div class="wrap">
  <h2>واجهة محادثة Weilix</h2>
  <div class="row" style="margin-bottom:8px">
    <label>قاعدة API:&nbsp;</label>
    <input id="base" value="http://127.0.0.1:8080">
    <button onclick="ping()">فحص</button>
    <span id="status"></span>
  </div>
  <textarea id="prompt" placeholder="اكتب رسالتك إلى Weilix…"></textarea>
  <div class="row" style="margin-top:8px">
    <button onclick="send()">إرسال</button>
  </div>
  <div id="out" class="log"></div>
</div>
<script>
  const $=id=>document.getElementById(id); const base=()=>$('base').value.replace(/\/$/,'');
  async function ping(){ const s=$('status'); try{const r=await fetch(base()+'/v1/health'); const j=await r.json(); s.textContent='OK • '+j.version;}catch{ s.textContent='فشل'; } }
  async function send(){
    const out=$('out'), prompt=$('prompt').value.trim(); out.textContent='جارٍ الإرسال…';
    try{
      const r=await fetch(base()+'/v1/infer',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({prompt,context:[]})});
      const j=await r.json(); out.textContent=JSON.stringify(j,null,2);
    }catch(e){ out.textContent='خطأ: '+e; }
  }
  ping();
</script>
</body></html>
