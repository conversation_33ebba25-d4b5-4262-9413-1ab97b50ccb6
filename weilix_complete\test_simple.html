<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>اختبار بسيط لويليكس</title>
</head>
<body>
    <h1>اختبار بسيط</h1>
    <button onclick="testAPI()">اختبار API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'جاري الاختبار...';
            
            try {
                // اختبار 1: فحص الصحة
                console.log('اختبار فحص الصحة...');
                const healthRes = await fetch('http://127.0.0.1:8080/v1/health');
                const healthData = await healthRes.json();
                console.log('نتيجة فحص الصحة:', healthData);
                
                // اختبار 2: إرسال رسالة
                console.log('اختبار إرسال رسالة...');
                const inferRes = await fetch('http://127.0.0.1:8080/v1/infer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: 'مرحبا',
                        context: []
                    })
                });
                
                const inferData = await inferRes.json();
                console.log('نتيجة الرسالة:', inferData);
                
                resultDiv.innerHTML = `
                    <h3>نتائج الاختبار:</h3>
                    <p><strong>فحص الصحة:</strong> ${JSON.stringify(healthData)}</p>
                    <p><strong>الرسالة:</strong> ${JSON.stringify(inferData)}</p>
                    <p><strong>الرد:</strong> ${inferData.output || 'لا يوجد رد'}</p>
                `;
                
            } catch (error) {
                console.error('خطأ:', error);
                resultDiv.innerHTML = `<p style="color: red;">خطأ: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
